import pluginJs from '@eslint/js';
import eslintPluginImport from 'eslint-plugin-import';
import globals from 'globals';
import tseslint from 'typescript-eslint';
import pluginNode from 'eslint-plugin-node';

/** @type {import('eslint').Linter.Config[]} */
export default [
	{ files: ['**/*.{js,mjs,cjs,ts}'] },
	{ languageOptions: { globals: globals.node } },
	pluginJs.configs.recommended,
	...tseslint.configs.recommended,
	{
		ignores: ['shared', 'types', 'docs', 'logs'],
		plugins: {
			import: eslintPluginImport,
			node: pluginNode,
		},
		rules: {
			'node/no-process-env': 'error',
			'@typescript-eslint/no-explicit-any': 'warn',
			'@typescript-eslint/no-require-imports': 'warn',
			'@typescript-eslint/no-empty-object-type': 'warn',
			'indent': ['error', 'tab', { SwitchCase: 1 }],
			'no-mixed-spaces-and-tabs': 'error',

			'no-warning-comments': [
				'warn',
				{
					'terms': ['todo', 'fixme'],
					'location': 'start'
				}
			],

			'@typescript-eslint/no-unused-vars': [
				'warn',
				{
					vars: 'all', // Check all variables
					args: 'after-used', // Ignore unused arguments after the last used argument
					argsIgnorePattern: '^_', // Ignore variables prefixed with _
					ignoreRestSiblings: true // Ignore unused siblings in rest destructuring
				}
			],

			// Rule to enforce no extensions for TS imports (disabled as the fix script has already been run)
			'import/extensions': [
				'off',
				'ignorePackages',
				{ ts: 'never' }
			],
		},
		settings: {
			'import/resolver': {
				typescript: {
					alwaysTryTypes: true, // Resolve type definitions as well
				},
			},
		},
	}
];