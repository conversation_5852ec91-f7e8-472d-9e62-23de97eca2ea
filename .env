# MongoDB URIs
MONGODB_URI_UNRAID=***********************************************************************************************
MONGODB_URI_LOCAL=mongodb://localhost:27017/introspection
MONGODB_URI_STAGNG=NONE
MONGODB_URI_PROD=NONE

# Safer MongoDb Credentials
MONGODB_URI=***********************************************************************************************
MONGO_USER=admin
MONGO_PASS="u?yNxEWg#3"
MONGO_HOST=************
MONGO_PORT=27017
MONGO_DB_NAME=introspection
MONGO_AUTH_SOURCE=admin

# Service Identification
SERVICE_NAME=excelytics.finance
API_VERSION=v1
VERSION=1.1.2

# Environment & Network
TAILSCALE_IP=************
ENV=development
PORT=6001
VPN=true# Flag to use Unraid/Tailscale IPs

# Redis Configuration
REDIS_URI_UNRAID=************
REDIS_URI=************
REDIS_HOST=************
REDIS_PASSWORD=undefined
REDIS_PORT=6379

# Microservices URLs (Revised Ports)
IDP_SERVICE_URL=http://localhost:6002
CALC_SERVICE_URL=http://localhost:6003
CLIENT_SERVICE_URL=http://localhost:4200
FINANCE_SERVICE_URL=http://localhost:6001

# --- Thresholds & Expiration Times (5s & 1h) ---
SLOW_REQUEST_THRESHOLD_MS=5000
SESSION_MAX_AGE_MS=3600000

# Gateway configuration
HEALTH_CHECK_INTERVAL=30000
REQUEST_TIMEOUT=5000
MAX_RETRIES=3

# Log Levels: (TRACE, DEBUG, INFO, WARN, ERROR, SILENT)
LOG_LEVEL=DEBUG

# Specific To Tests
SKIP_AUTHENTICATION=true

# For private package resolution in bunfig.toml
ADO_PAT="81b4PpIvFBRfyk6emwZHgpMUAEEFZGXKcps35PXpcebqsHGLOQcCJQQJ99BEACAAAAAAAAAAAAASAZDO2fLb"