import { MongoMemoryReplSet } from 'mongodb-memory-server';
import mongoose from 'mongoose';

let replSet: MongoMemoryReplSet;

export async function setupTestDatabase() {
    // Create in-memory MongoDB replica set
    replSet = await MongoMemoryReplSet.create({
        replSet: { count: 1, name: 'testrs' },
        instanceOpts: [
            {
                port: 27019, // Different from your main MongoDB
                dbName: 'introspection_test'
            }
        ]
    });

    const uri = replSet.getUri();
    console.log('🧪 Test MongoDB URI:', uri);

    // Connect to test database
    await mongoose.connect(uri);
    console.log('✅ Connected to test MongoDB replica set');

    return uri;
}

export async function teardownTestDatabase() {
    if (mongoose.connection.readyState !== 0) {
        await mongoose.disconnect();
    }
    
    if (replSet) {
        await replSet.stop();
    }
    
    console.log('🧹 Test database cleaned up');
}
