#!/usr/bin/env bun

/**
 * Quick script to verify MongoDB transaction support
 * Run with: bun tests/core/verify-transactions.ts
 */

import mongoose from 'mongoose';
import { connectDB } from '@/shared/config/database';

async function verifyTransactions() {
    console.log('🔍 Checking MongoDB transaction support...\n');
    
    try {
        // Connect to database
        await connectDB();
        console.log('✅ Connected to MongoDB');
        
        // Check if we're connected to a replica set
        const admin = mongoose.connection.db.admin();
        const serverStatus = await admin.command({ serverStatus: 1 });
        
        console.log(`📊 MongoDB Version: ${serverStatus.version}`);
        console.log(`🏗️  Storage Engine: ${serverStatus.storageEngine?.name || 'Unknown'}`);
        
        // Try to get replica set status
        try {
            const replSetStatus = await admin.command({ replSetGetStatus: 1 });
            console.log(`🔗 Replica Set: ${replSetStatus.set}`);
            console.log(`👑 Primary: ${replSetStatus.members.find((m: any) => m.stateStr === 'PRIMARY')?.name || 'Unknown'}`);
            console.log(`📊 Members: ${replSetStatus.members.length}`);
        } catch (error) {
            console.log('⚠️  Not running as replica set');
        }
        
        // Test session creation
        console.log('\n🧪 Testing session creation...');
        const session = await mongoose.startSession();
        console.log('✅ Session created successfully');
        
        // Test transaction
        console.log('🧪 Testing transaction...');
        try {
            await session.startTransaction();
            console.log('✅ Transaction started successfully');
            
            await session.abortTransaction();
            console.log('✅ Transaction aborted successfully');
            
            console.log('\n🎉 MongoDB transactions are fully supported!');
            console.log('✅ You can run the full transaction test suite');
            
        } catch (transactionError: any) {
            console.log('❌ Transaction failed:', transactionError.message);
            
            if (transactionError.message.includes('replica set member')) {
                console.log('\n💡 Solution: Set up MongoDB as a replica set');
                console.log('   See: tests/core/README-MongoDB-Transactions.md');
            }
        } finally {
            await session.endSession();
        }
        
    } catch (error: any) {
        console.error('❌ Error:', error.message);
    } finally {
        await mongoose.disconnect();
        console.log('\n🔌 Disconnected from MongoDB');
    }
}

// Run the verification
verifyTransactions().catch(console.error);
