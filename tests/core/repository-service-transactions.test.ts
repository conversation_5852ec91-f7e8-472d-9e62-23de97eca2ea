import { describe, test, expect, beforeAll, afterAll, beforeEach } from 'bun:test';
import mongoose, { type ClientSession } from 'mongoose';
import { Types } from 'mongoose';
import { connectDB } from '@/shared/config/database';
import { UserRepository } from '@/services/repositories/internal/user.repository';
import { OrganizationRepository } from '@/services/repositories/internal/organization.repository';
import { User, UserModel, Organization, OrganizationModel } from '@/models/internal';
import { EnumUserTypes, EnumEditions } from 'excelytics.shared-models';
import { RedisService } from 'excelytics.shared-internals';

describe('Repository Service Transaction Tests', () => {
	let userRepository: UserRepository;
	let organizationRepository: OrganizationRepository;
	let testOrganizationId: string;
	let redisService: RedisService;
	let isReplicaSet: boolean = false;

	beforeAll(async () => {
		// Connect to test database
		await connectDB();

		// Initialize Redis service
		redisService = RedisService.getInstance();

		// Initialize repositories
		userRepository = new UserRepository();
		organizationRepository = new OrganizationRepository();

		// Check if MongoDB is running as replica set
		try {
			const session = await userRepository.startSession();
			await session.startTransaction();
			await session.abortTransaction();
			await session.endSession();
			isReplicaSet = true;
			console.log('✅ MongoDB replica set detected - transaction tests will run');
		} catch (error) {
			isReplicaSet = false;
			console.log('⚠️  MongoDB standalone detected - transaction tests will be skipped');
		}

		// Create a test organization for user references
		const testOrg: Partial<Organization> = {
			organizationName: 'Test Organization for Transactions',
			editionCode: EnumEditions.Advanced
		};

		const createdOrg = await organizationRepository.create(testOrg);
		testOrganizationId = createdOrg._id.toString();
	});

	afterAll(async () => {
		// Clean up test data
		try {
			await UserModel.deleteMany({ email: { $regex: /^test.*transaction/ } });
			await OrganizationModel.deleteMany({ organizationName: { $regex: /Test Organization/ } });
		} catch (error) {
			console.warn('Cleanup warning:', error);
		}
		
		// Close connections
		await mongoose.disconnect();
		await redisService.disconnect();
	});

	beforeEach(async () => {
		// Clean up any test users before each test
		await UserModel.deleteMany({ email: { $regex: /^test.*transaction/ } });
	});

	describe('Basic Transaction Operations', () => {
		test('should create user with transaction session', async () => {
			const session = await userRepository.startSession();
			
			try {
				await session.startTransaction();

				const userData: Partial<User> = {
					email: '<EMAIL>',
					firstName: 'Test',
					lastName: 'User',
					cellNumber: '1234567890',
					userTypeCode: EnumUserTypes.Employee,
					editionCode: EnumEditions.Simple,
					fkOrganizationId: new Types.ObjectId(testOrganizationId),
					isActive: true
				};

				const createdUser = await userRepository.create(userData, { session });
				
				expect(createdUser).toBeDefined();
				expect(createdUser.email).toBe(userData.email);
				expect(createdUser._id).toBeDefined();

				await session.commitTransaction();

				// Verify user exists in database
				const foundUser = await userRepository.findById(createdUser._id.toString());
				expect(foundUser).toBeDefined();
				expect(foundUser?.email).toBe(userData.email);

			} catch (error) {
				await session.abortTransaction();
				throw error;
			} finally {
				await session.endSession();
			}
		});

		test('should update user with transaction session', async () => {
			// First create a user
			const userData: Partial<User> = {
				email: '<EMAIL>',
				firstName: 'Test',
				lastName: 'User',
				cellNumber: '1234567891',
				userTypeCode: EnumUserTypes.Employee,
				editionCode: EnumEditions.Simple,
				fkOrganizationId: new Types.ObjectId(testOrganizationId),
				isActive: true
			};

			const createdUser = await userRepository.create(userData);
			const userId = createdUser._id.toString();

			// Now test update with transaction
			const session = await userRepository.startSession();
			
			try {
				await session.startTransaction();

				const updateData = {
					firstName: 'Updated',
					lastName: 'Name',
					userTypeCode: EnumUserTypes.Admin
				};

				const updatedUser = await userRepository.update(userId, updateData, { session });
				
				expect(updatedUser).toBeDefined();
				expect(updatedUser.firstName).toBe('Updated');
				expect(updatedUser.lastName).toBe('Name');
				expect(updatedUser.userTypeCode).toBe(EnumUserTypes.Admin);

				await session.commitTransaction();

				// Verify changes persisted
				const foundUser = await userRepository.findById(userId);
				expect(foundUser?.firstName).toBe('Updated');
				expect(foundUser?.lastName).toBe('Name');

			} catch (error) {
				await session.abortTransaction();
				throw error;
			} finally {
				await session.endSession();
			}
		});

		test('should delete user with transaction session', async () => {
			// First create a user
			const userData: Partial<User> = {
				email: '<EMAIL>',
				firstName: 'Test',
				lastName: 'User',
				cellNumber: '1234567892',
				userTypeCode: EnumUserTypes.Employee,
				editionCode: EnumEditions.Simple,
				fkOrganizationId: new Types.ObjectId(testOrganizationId),
				isActive: true
			};

			const createdUser = await userRepository.create(userData);
			const userId = createdUser._id.toString();

			// Now test delete with transaction
			const session = await userRepository.startSession();
			
			try {
				await session.startTransaction();

				await userRepository.delete(userId, { session });

				await session.commitTransaction();

				// Verify user is deleted
				const foundUser = await userRepository.findById(userId);
				expect(foundUser).toBeNull();

			} catch (error) {
				await session.abortTransaction();
				throw error;
			} finally {
				await session.endSession();
			}
		});
	});

	describe('Multiple Operations in Single Transaction', () => {
		test('should perform multiple creates in single transaction', async () => {
			const session = await userRepository.startSession();
			
			try {
				await session.startTransaction();

				const users: Partial<User>[] = [
					{
						email: '<EMAIL>',
						firstName: 'User',
						lastName: 'One',
						cellNumber: '1234567893',
						userTypeCode: EnumUserTypes.Employee,
						editionCode: EnumEditions.Simple,
						fkOrganizationId: new Types.ObjectId(testOrganizationId),
						isActive: true
					},
					{
						email: '<EMAIL>',
						firstName: 'User',
						lastName: 'Two',
						cellNumber: '1234567894',
						userTypeCode: EnumUserTypes.Company,
						editionCode: EnumEditions.Advanced,
						fkOrganizationId: new Types.ObjectId(testOrganizationId),
						isActive: true
					}
				];

				const createdUsers = [];
				for (const userData of users) {
					const user = await userRepository.create(userData, { session });
					createdUsers.push(user);
				}

				expect(createdUsers).toHaveLength(2);
				expect(createdUsers[0].email).toBe(users[0].email);
				expect(createdUsers[1].email).toBe(users[1].email);

				await session.commitTransaction();

				// Verify both users exist
				for (const user of createdUsers) {
					const foundUser = await userRepository.findById(user._id.toString());
					expect(foundUser).toBeDefined();
				}

			} catch (error) {
				await session.abortTransaction();
				throw error;
			} finally {
				await session.endSession();
			}
		});
	});

	describe('Transaction Rollback Scenarios', () => {
		test('should rollback transaction on duplicate email error', async () => {
			// First create a user
			const userData: Partial<User> = {
				email: '<EMAIL>',
				firstName: 'Original',
				lastName: 'User',
				cellNumber: '1234567895',
				userTypeCode: EnumUserTypes.Employee,
				editionCode: EnumEditions.Simple,
				fkOrganizationId: new Types.ObjectId(testOrganizationId),
				isActive: true
			};

			await userRepository.create(userData);

			// Now try to create another user with same email in transaction
			const session = await userRepository.startSession();
			
			try {
				await session.startTransaction();

				// This should fail due to unique email constraint
				const duplicateUserData: Partial<User> = {
					...userData,
					firstName: 'Duplicate',
					cellNumber: '1234567896'
				};

				await expect(async () => {
					await userRepository.create(duplicateUserData, { session });
				}).toThrow();

				await session.abortTransaction();

			} catch (error) {
				await session.abortTransaction();
				// This is expected
			} finally {
				await session.endSession();
			}

			// Verify only original user exists
			const users = await userRepository.find({ email: userData.email });
			expect(users).toHaveLength(1);
			expect(users[0].firstName).toBe('Original');
		});

		test('should rollback multiple operations when one fails', async () => {
			const session = await userRepository.startSession();
			
			try {
				await session.startTransaction();

				// Create first user (should succeed)
				const user1Data: Partial<User> = {
					email: '<EMAIL>',
					firstName: 'User',
					lastName: 'One',
					cellNumber: '1234567897',
					userTypeCode: EnumUserTypes.Employee,
					editionCode: EnumEditions.Simple,
					fkOrganizationId: new Types.ObjectId(testOrganizationId),
					isActive: true
				};

				const user1 = await userRepository.create(user1Data, { session });
				expect(user1).toBeDefined();

				// Try to create second user with invalid data (should fail)
				const user2Data: Partial<User> = {
					email: 'invalid-email', // Invalid email format
					firstName: 'User',
					lastName: 'Two',
					cellNumber: '1234567898',
					userTypeCode: EnumUserTypes.Employee,
					editionCode: EnumEditions.Simple,
					fkOrganizationId: new Types.ObjectId(testOrganizationId),
					isActive: true
				};

				await expect(async () => {
					await userRepository.create(user2Data, { session });
				}).toThrow();

				await session.abortTransaction();

			} catch (error) {
				await session.abortTransaction();
			} finally {
				await session.endSession();
			}

			// Verify neither user exists (transaction was rolled back)
			const users1 = await userRepository.find({ email: '<EMAIL>' });
			const users2 = await userRepository.find({ email: 'invalid-email' });
			
			expect(users1).toHaveLength(0);
			expect(users2).toHaveLength(0);
		});
	});

	describe('Session Management', () => {
		test('should properly start and end sessions', async () => {
			const session = await userRepository.startSession();
			
			expect(session).toBeDefined();
			expect(session.id).toBeDefined();
			
			await session.endSession();
		});

		test('should handle session reuse across operations', async () => {
			const session = await userRepository.startSession();
			
			try {
				await session.startTransaction();

				// Create user
				const userData: Partial<User> = {
					email: '<EMAIL>',
					firstName: 'Session',
					lastName: 'Test',
					cellNumber: '1234567899',
					userTypeCode: EnumUserTypes.Employee,
					editionCode: EnumEditions.Simple,
					fkOrganizationId: new Types.ObjectId(testOrganizationId),
					isActive: true
				};

				const createdUser = await userRepository.create(userData, { session });
				const userId = createdUser._id.toString();

				// Update same user in same session
				const updatedUser = await userRepository.update(userId, { firstName: 'Updated' }, { session });
				
				expect(updatedUser.firstName).toBe('Updated');

				await session.commitTransaction();

				// Verify final state
				const foundUser = await userRepository.findById(userId);
				expect(foundUser?.firstName).toBe('Updated');

			} catch (error) {
				await session.abortTransaction();
				throw error;
			} finally {
				await session.endSession();
			}
		});
	});
});