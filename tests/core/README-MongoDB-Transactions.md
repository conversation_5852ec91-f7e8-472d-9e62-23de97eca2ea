# MongoDB Transactions Setup Guide

## Overview

MongoDB transactions require a **replica set** or **sharded cluster**. They do NOT work with standalone MongoDB instances.

## Current Test Results

The repository service tests are designed to:
- ✅ **Work with standalone MongoDB** (basic CRUD operations, session management)
- ✅ **Work with replica sets** (full transaction support)
- ⚠️ **Automatically detect** your MongoDB setup and skip transaction tests if needed

## Why Transactions Need Replica Sets

1. **Oplog Requirement**: Transactions need the operations log (oplog) for rollback capability
2. **Consistency**: Replica sets provide the distributed consensus needed for ACID transactions
3. **MongoDB Design**: Standalone instances don't maintain oplogs

## Setting Up MongoDB Replica Set for Development

### Option 1: Docker Compose (Recommended for Development)

Create a `docker-compose.yml` file:

```yaml
version: '3.8'
services:
  mongo1:
    image: mongo:7.0
    container_name: mongo1
    command: mongod --replSet rs0 --port 27017
    ports:
      - "27017:27017"
    volumes:
      - mongo1_data:/data/db
    networks:
      - mongo-cluster

  mongo2:
    image: mongo:7.0
    container_name: mongo2
    command: mongod --replSet rs0 --port 27018
    ports:
      - "27018:27018"
    volumes:
      - mongo2_data:/data/db
    networks:
      - mongo-cluster

  mongo3:
    image: mongo:7.0
    container_name: mongo3
    command: mongod --replSet rs0 --port 27019
    ports:
      - "27019:27019"
    volumes:
      - mongo3_data:/data/db
    networks:
      - mongo-cluster

volumes:
  mongo1_data:
  mongo2_data:
  mongo3_data:

networks:
  mongo-cluster:
    driver: bridge
```

Start the cluster:
```bash
docker-compose up -d
```

Initialize the replica set:
```bash
docker exec -it mongo1 mongosh --eval "
rs.initiate({
  _id: 'rs0',
  members: [
    { _id: 0, host: 'mongo1:27017' },
    { _id: 1, host: 'mongo2:27018' },
    { _id: 2, host: 'mongo3:27019' }
  ]
})
"
```

### Option 2: Single Node Replica Set (Simpler for Testing)

Start MongoDB with replica set:
```bash
mongod --replSet rs0 --port 27017 --dbpath /data/db
```

Initialize single-node replica set:
```bash
mongosh --eval "rs.initiate()"
```

### Option 3: MongoDB Atlas (Cloud)

Use MongoDB Atlas which provides replica sets by default.

## Updating Your Connection String

For replica set, update your `.env`:

```env
# For local replica set
MONGODB_URI=mongodb://localhost:27017,localhost:27018,localhost:27019/introspection?replicaSet=rs0

# For single-node replica set
MONGODB_URI=mongodb://localhost:27017/introspection?replicaSet=rs0
```

## Testing Transaction Functionality

Once you have a replica set running:

1. **Run the tests**:
   ```bash
   bun test tests/core/repository-service-transactions.test.ts
   ```

2. **Expected output**:
   ```
   ✅ MongoDB replica set detected - transaction tests will run
   ✓ should create user with transaction session
   ✓ should update user with transaction session
   ✓ should delete user with transaction session
   ✓ should perform multiple creates in single transaction
   ✓ should rollback transaction on duplicate email error
   ✓ should rollback multiple operations when one fails
   ✓ should handle session reuse across operations
   ```

## Transaction Best Practices

1. **Keep transactions short** - Long transactions can impact performance
2. **Handle retries** - Network issues can cause transaction failures
3. **Use appropriate read/write concerns** - Ensure consistency requirements
4. **Monitor performance** - Transactions add overhead

## Troubleshooting

### Error: "Transaction numbers are only allowed on a replica set member or mongos"
- **Cause**: You're running on standalone MongoDB
- **Solution**: Set up replica set as described above

### Error: "No primary available"
- **Cause**: Replica set not properly initialized
- **Solution**: Run `rs.initiate()` or check replica set status with `rs.status()`

### Connection Issues
- **Check**: Ensure all replica set members are accessible
- **Verify**: Connection string includes all members and replica set name

## Current Test Coverage

The test suite covers:

### ✅ Basic Operations (Works with Standalone)
- Create, Read, Update, Delete operations
- Session management
- Error handling

### ✅ Transaction Operations (Requires Replica Set)
- Transactional CRUD operations
- Multi-operation transactions
- Transaction rollback scenarios
- Session reuse across operations

### ✅ Error Scenarios
- Duplicate key errors
- Invalid data validation
- Network failures
- Transaction conflicts
