const { MongoClient } = require('mongodb');

async function testConnection() {
    const uri = "***********************************************************************************************";
    
    console.log('Testing connection to:', uri);
    
    const client = new MongoClient(uri, {
        serverSelectionTimeoutMS: 5000, // 5 second timeout
    });

    try {
        await client.connect();
        console.log('✅ Connected successfully');
        
        // Test replica set status
        const admin = client.db().admin();
        const status = await admin.command({ replSetGetStatus: 1 });
        console.log('✅ Replica set status:', status.set);
        console.log('✅ Primary:', status.members.find(m => m.stateStr === 'PRIMARY')?.name);
        
        // Test transaction
        const session = client.startSession();
        session.startTransaction();
        console.log('✅ Transaction started successfully');
        await session.abortTransaction();
        await session.endSession();
        console.log('✅ Transaction test passed');
        
    } catch (error) {
        console.error('❌ Connection failed:', error.message);
        
        if (error.message.includes('ReplicaSetNoPrimary')) {
            console.log('\n💡 Solutions:');
            console.log('1. Check if replica set is initialized: rs.status()');
            console.log('2. Make sure MongoDB is accessible from your IP');
            console.log('3. Verify the replica set name matches your connection string');
        }
    } finally {
        await client.close();
    }
}

testConnection();
