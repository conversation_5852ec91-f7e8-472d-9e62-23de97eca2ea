# 📊 Project Analysis: Introspection.Finance

## 📋 Summary
- **Generated:** Wed, 18 Jun 2025 21:24:28 GMT
- **Analysis Duration:** 8ms
- **Root Path:** `/Users/<USER>/dev_code_directory/Introspection/Introspection.Finance`

## 🔍 Git Information
**Branch:** Reza/xxx-fix-tests-v2--master | **Commit:** 810aea9 | **Total Commits:** 306

## 📦 Package Information  
**Dependencies:** 24 | **Dev Dependencies:** 16 | **Version:** 1.1.2

## 📈 Project Metrics

### 📊 Overview
- **Total Files:** 132
- **Total Directories:** 48
- **Total Size:** 150.0 KB
- **Lines of Code:** 4,314

### 📋 File Distribution
| Extension | Files | Lines of Code |
|-----------|-------|---------------|
| .ts | 126 | 4,176 |
| no ext | 3 | 0 |
| .json | 2 | 138 |
| .mjs | 1 | 0 |

### 📂 Largest Files
| File | Size | Path |
|------|------|------|
| repository.service.ts | 10.0 KB | src/core/common/repository.service.ts |
| audit-log.service.ts | 7.0 KB | src/services/mutators/internal/audit-log.service.ts |
| mutator.service.ts | 5.9 KB | src/core/common/mutator.service.ts |
| service.interface.ts | 4.5 KB | src/shared/interfaces/service.interface.ts |
| generic.controller.ts | 4.2 KB | src/core/common/generic.controller.ts |
| odata.helper.ts | 3.6 KB | src/shared/helpers/odata.helper.ts |
| company-directory.repository.ts | 3.5 KB | src/services/repositories/external/company-directory.repository.ts |
| company-directory.validator.ts | 3.3 KB | src/core/validators/external/company-directory.validator.ts |
| server.ts | 3.3 KB | src/server.ts |
| security-options.middleware.ts | 3.2 KB | src/middleware/security-options.middleware.ts |

### 🕐 Recently Modified Files
| File | Modified | Path |
|------|----------|------|
| uri.constants.ts | 6/18/2025 | src/shared/constants/uri.constants.ts |
| server.ts | 6/18/2025 | src/server.ts |
| rate-limiter.middleware.ts | 6/18/2025 | src/middleware/rate-limiter.middleware.ts |
| index.ts | 6/18/2025 | src/middleware/index.ts |
| cli.ts | 6/18/2025 | src/shared/scripts/cli.ts |

## 🌳 Directory Structure
```
Introspection.Finance/
├── types/
│   ├── db.types.ts
│   ├── odata.types.ts
│   ├── env_.ts
│   └── express.d.ts
├── .npmrc
├── package.json
├── .env
├── .prettierrc
├── tsconfig.json
├── eslint.config.mjs
└── src/
    ├── middleware/
    │   ├── security-options.middleware.ts
    │   ├── authentication.middleware.ts
    │   ├── rate-limiter.middleware.ts
    │   ├── index.ts
    │   └── custom.middleware.ts
    ├── core/
    │   ├── common/
    │   │   ├── repository.service.ts
    │   │   ├── generic.controller.ts
    │   │   ├── mutator.service.ts
    │   │   ├── odata-metadata-service.ts
    │   │   └── generic.service.ts
    │   └── validators/
    │       └── external/
    │           └── company-directory.validator.ts
    ├── shared/
    │   ├── config/
    │   │   └── database.ts
    │   ├── constants/
    │   │   ├── uri.constants.ts
    │   │   └── localizations/
    │   │       └── messages.ts
    │   ├── scripts/
    │   │   ├── cli.ts
    │   │   └── maintenance/
    │   │       ├── drop-tables.ts
    │   │       ├── populate/
    │   │       │   ├── populate-tables.ts
    │   │       │   ├── internal/
    │   │       │   │   ├── organization.populate.ts
    │   │       │   │   ├── user.populate.ts
    │   │       │   │   └── index.ts
    │   │       │   └── external/
    │   │       │       ├── company-group.populate.ts
    │   │       │       ├── company-directory.populate.ts
    │   │       │       ├── company.populate.ts
    │   │       │       ├── index.ts
    │   │       │       └── individual.populate.ts
    │   │       └── seeds/
    │   │           ├── seed-enums.ts
    │   │           └── enums/
    │   │               ├── enum-subcategory.savings.seed.ts
    │   │               ├── enum-subcategory.income.seed.ts
    │   │               ├── enum-subcategory.expenses.seed.ts
    │   │               ├── enum-user-type.seed.ts
    │   │               ├── index.ts
    │   │               ├── enum-category.seed.ts
    │   │               ├── enum-account-type.seed.ts
    │   │               └── enum-edition.seed.ts
    │   ├── helpers/
    │   │   ├── odata.helper.ts
    │   │   ├── authentication.helper.ts
    │   │   └── error.helper.ts
    │   └── interfaces/
    │       └── service.interface.ts
    ├── models/
    │   ├── internal/
    │   │   ├── user.model.ts
    │   │   ├── organization-access.model.ts
    │   │   ├── organization.model.ts
    │   │   ├── audit-log.model.ts
    │   │   └── index.ts
    │   └── data/
    │       ├── finance-raw.model.ts
    │       ├── finance-category-total.model.ts
    │       ├── index.ts
    │       └── finance-category-summary.model.ts
    ├── schemas/
    │   ├── enums/
    │   │   └── enum-category.schema.ts
    │   ├── internal/
    │   │   ├── audit-trail.schema.ts
    │   │   ├── user-validation.schema.ts
    │   │   └── organization.schema.ts
    │   └── data/
    │       ├── finance-raw.schema.ts
    │       ├── finance-category-summary.schema.ts
    │       └── finance-category-total.schema.ts
    ├── controllers/
    │   ├── enums/
    │   │   ├── enum-edition.controller.ts
    │   │   ├── enum-account-type.controller.ts
    │   │   ├── enum-user-type.controller.ts
    │   │   ├── enum-category.controller.ts
    │   │   └── enum-subcategory.controller.ts
    │   ├── internal/
    │   │   ├── user.controller.ts
    │   │   ├── organization.controller.ts
    │   │   └── audit-log.controller.ts
    │   ├── external/
    │   │   ├── company.controller.ts
    │   │   ├── company-directory.controller.ts
    │   │   ├── individual.controller.ts
    │   │   └── company-group.controller.ts
    │   └── data/
    │       ├── finance-category-summary.controller.ts
    │       ├── finance-raw.controller.ts
    │       └── finance-category-total.controller.ts
    ├── server.ts
    ├── routes/
    │   ├── enums/
    │   │   ├── enum-account-type.route.ts
    │   │   ├── enum-category.route.ts
    │   │   ├── enum-edition.route.ts
    │   │   ├── enum-subcategory.route.ts
    │   │   ├── enum-user-type.route.ts
    │   │   └── index.ts
    │   ├── internal/
    │   │   ├── audit-log.route.ts
    │   │   ├── index.ts
    │   │   ├── user.route.ts
    │   │   └── organization.route.ts
    │   ├── health.routes.ts
    │   ├── base.routes.ts
    │   ├── index.ts
    │   ├── external/
    │   │   ├── company-directory.route.ts
    │   │   ├── individual.route.ts
    │   │   ├── index.ts
    │   │   ├── company.route.ts
    │   │   └── company-group.route.ts
    │   └── data/
    │       ├── finance-category-summary.route.ts
    │       ├── finance-category-total.route.ts
    │       ├── finance-raw.route.ts
    │       └── index.ts
    └── services/
        ├── repositories/
        │   ├── enums/
        │   │   ├── enum-subcategory.repository.ts
        │   │   ├── enum-category.repository.ts
        │   │   ├── enum-user-type.repository.ts
        │   │   ├── enum-account-type.repository.ts
        │   │   └── enum-edition.repository.ts
        │   ├── internal/
        │   │   ├── audit-log.repository.ts
        │   │   ├── organization.repository.ts
        │   │   └── user.repository.ts
        │   ├── external/
        │   │   ├── company-group.repository.ts
        │   │   ├── individual.repository.ts
        │   │   ├── company-directory.repository.ts
        │   │   └── company.repository.ts
        │   └── data/
        │       ├── finance-category-total.repository.ts
        │       ├── finance-raw.repository.ts
        │       └── finance-category-summary.repository.ts
        └── mutators/
            ├── enums/
            │   ├── enum-edition.service.ts
            │   ├── enum-account-type.service.ts
            │   ├── enum-user-type.service.ts
            │   ├── enum-subcategory.service.ts
            │   └── enum-category.service.ts
            ├── internal/
            │   ├── organization.service.ts
            │   ├── audit-log.service.ts
            │   └── user.service.ts
            ├── external/
            │   ├── individual.service.ts
            │   ├── company-directory.service.ts
            │   ├── company.service.ts
            │   └── company-group.service.ts
            └── data/
                ├── finance-raw.service.ts
                ├── finance-category-total.service.ts
                └── finance-category-summary.service.ts

```

## 🏗️ Architecture Notes
This is part of the **Introspection Consulting** microservices architecture:
- **Backend (BE):** Introspection.Finance
- **Identity Provider (IdP):** Introspection.Finance.Identity  
- **Calculation Engine:** Introspection.Finance.Calc
- **Frontend (FE):** Introspection.Finance.Client

**Tech Stack:** MongoDB, TypeScript, Shadcn/ui, Bun
**Deployment:** Unraid Server with Nginx Gateway

---
*Generated by Enhanced Project Structure Analyzer*
