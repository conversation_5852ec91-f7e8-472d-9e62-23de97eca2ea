import { AuthenticationHelper } from '@/shared/helpers/authentication.helper';
import { env_finance } from '@app-types/env_';
import {
	type IntrospectionResponse,
	type NextFunction,
	UnauthorizedError,
	type Response,
	type Request,
	HttpStatus,
	ErrorCodes,
	BaseError
} from 'excelytics.shared-internals';

export class AuthenticationMiddleware {
	private readonly idpBaseUrl: string;

	constructor() {
		this.idpBaseUrl = env_finance.IDP_SERVICE_URL!;
	}

	// Main authentication middleware
	public authenticate = async (request: Request, response: Response, next: NextFunction): Promise<void> => {
		try {
			const authHeader = request.headers.authorization;
			if (!authHeader || !authHeader.startsWith('Bearer ')) {
				return next(new UnauthorizedError('Authorization header is missing or malformed'));
			}

			const token = authHeader.split(' ')[1];
			if (!token) {
				return next(new UnauthorizedError('A valid Bearer token is required'));
			}

			// Introspect the token from IdP
			const introspectionResult = await this.introspectToken(token);
			if (!introspectionResult.active) {
				return next(new UnauthorizedError('Token is invalid or expired'));
			}

			// Map introspection result to our token payload format
			request.user = AuthenticationHelper.MapIntrospectionToAccessTokenPayload(introspectionResult);

			next();
		} catch (error) {
			console.error('[Auth Middleware] Error during authentication:', error);
			if (error instanceof BaseError) {
				return next(error);
			}

			return next(new BaseError(
				'An unexpected error occurred during authentication',
				HttpStatus.INTERNAL_SERVER_ERROR,
				ErrorCodes.AUTHENTICATION_FAILED,
				undefined,
				error
			));
		}
	};

	// Call IdP introspection endpoint
	private async introspectToken(token: string): Promise<IntrospectionResponse> {
		try {
			const response = await fetch(`${this.idpBaseUrl}/api/v1/auth/introspect`, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ token })
			});

			if (!response.ok) {
				throw new BaseError(
					'IdP introspection request failed',
					HttpStatus.SERVICE_UNAVAILABLE,
					ErrorCodes.EXTERNAL_SERVICE_ERROR,
					{ status: response.status, statusText: response.statusText }
				);
			}

			return await response.json();
		} catch (error) {
			if (error instanceof BaseError) {
				throw error;
			}

			throw new BaseError(
				'Failed to communicate with Identity Provider',
				HttpStatus.SERVICE_UNAVAILABLE,
				ErrorCodes.EXTERNAL_SERVICE_ERROR,
				undefined,
				error
			);
		}
	}
}

// Export singleton instance
export const authMiddleware = new AuthenticationMiddleware();