import helmet from 'helmet';
import { env_finance } from '@app-types/env_';
import { AllowedOrigins, EnumEnv } from 'excelytics.shared-internals';

/**
 * CORS (Cross-Origin Resource Sharing) configuration for the Finance Service.
 * This service is the main backend API.
 */
export const CorsConfiguration = {
	origin: function (origin: any, callback: any) {
		// Allow server-to-server, etc
		if (!origin) {
			return callback(null, true);
		}

		// Check if the requesting origin is in our allowed list
		if (AllowedOrigins.indexOf(origin) !== -1) {
			return callback(null, true);
		} else {
			console.warn(`CORS blocked request from origin: ${origin} for Finance Service`);
			return callback(new Error('This origin is not allowed by CORS.'));
		}
	},
	// HTTP methods allowed for CORS requests
	methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],

	// Headers clients are allowed to be exposed via CORS
	allowedHeaders: [
		'Content-Type',
		'Authorization', 	// For user JWTs
		'X-Request-ID', 	// For tracing
		'X-Client-Version', // To track client application versions
	],
	exposedHeaders: [
		'X-Request-ID',
		'X-RateLimit-Limit',
		'X-RateLimit-Remaining',
		'X-RateLimit-Reset',

		// Add custom informational headers set by EarlySecurityHeaders
		'X-Service',
		'X-API-Version',
		'X-Environment',
		'X-No-Compression' 			// If you want clients to know if compression was skipped
	],
	credentials: true, 				// Allow cookies & authentication headers to be sent with the requests
	maxAge: 86400, 					// Cache preflight request results for 24 hours (in seconds)

	// Provides a success status code for legacy browsers that may not handle 204 correctly for preflight OPTIONS requests
	optionsSuccessStatus: 200
};

/**
 * Helmet security configuration for the Finance Service.
 * As a pure JSON API, we apply very strict policies.
 */
export const HelmetConfiguration = helmet({
	contentSecurityPolicy: {
		directives: {
			defaultSrc: ["'none'"], 		// Disallow everything by default
			connectSrc: ["'self'"], 		// API can only connect to itself (if needed)
			formAction: ["'none'"], 		// Forms cannot be submitted from this origin
			frameAncestors: ["'none'"], 	// Modern replacement for X-Frame-Options

			// Explicitly block all other content types
			imgSrc: ["'none'"],
			fontSrc: ["'none'"],
			baseUri: ["'self'"],
			mediaSrc: ["'none'"],
			styleSrc: ["'none'"],
			scriptSrc: ["'none'"],
			objectSrc: ["'none'"],
			workerSrc: ["'none'"],
			manifestSrc: ["'none'"],

			// In production, instruct browsers to upgrade all HTTP requests to HTTPS
			upgradeInsecureRequests: env_finance.ENV === EnumEnv.Production ? [] : null
		}
	},

	// --- Other Security Policies ---
	crossOriginResourcePolicy: { policy: 'same-origin' },
	crossOriginOpenerPolicy: { policy: 'same-origin' },
	dnsPrefetchControl: { allow: false },
	crossOriginEmbedderPolicy: false,
	hidePoweredBy: true,

	// Disabled in favor of CSP frame-ancestors
	frameguard: false,

	// HTTP Strict Transport Security (HSTS) - Enforces HTTPS connections
	hsts: {
		includeSubDomains: true,	// Apply to all subdomains
		maxAge: 31536000, 			// 1 year in seconds
		preload: true				// Agree to be included in browser HSTS preload list
	},
	noSniff: true,

	// Strictest policy, best for APIs
	referrerPolicy: { policy: 'no-referrer' }
});