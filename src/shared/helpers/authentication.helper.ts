import {
	type IntrospectionResponse,
	type AccessTokenPayload,
	EnumClientOrigin,
	EnumC<PERSON>Path,
	EnumTokenType
} from 'excelytics.shared-internals';

/**
 * Maps IdP introspection response to your AccessTokenPayload type
 */
function MapIntrospectionToAccessTokenPayload(introspection: IntrospectionResponse): AccessTokenPayload {
	return {
		// -- BaseTokenPayload fields --
		userId: introspection.sub!, 		// Subject identifier
		email: introspection.username!,
		clientId: introspection.client_id!,

		// -- Enum fields --
		clientOrigin: (introspection.client_origin as EnumClientOrigin) || EnumClientOrigin.Excelytics,
		clientPath: (introspection.client_path as EnumClientPath) || EnumClientPath.Finance,

		isActive: true,

		// -- AccessTokenPayload specific fields --
		tokenType: EnumTokenType.ACCESS,
		permissions: introspection.scope ? introspection.scope.split(' ') : [],
		issuedAt: introspection.iat ? new Date(introspection.iat * 1000) : new Date(),
		expiresAt: introspection.exp ? new Date(introspection.exp * 1000) : undefined
	};
}

export const AuthenticationHelper = {
	MapIntrospectionToAccessTokenPayload
};