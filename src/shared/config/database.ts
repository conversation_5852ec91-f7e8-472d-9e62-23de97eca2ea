import mongoose from 'mongoose';
import { env_finance } from '@app-types/env_';

const connectionString: string = env_finance.MONGODB_URI;

export const connectDB = async () => {
	try {
		// Connect to MongoDB
		await mongoose.connect(connectionString);
		console.log('-- Connected to MongoDB');

	} catch (err) {
		console.error('-- Error during database initialization:', err);
		process.exit(1);
	}
};