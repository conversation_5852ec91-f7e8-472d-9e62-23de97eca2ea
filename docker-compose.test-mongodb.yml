version: '3.8'
services:
  # Test MongoDB with transaction support
  mongo-test:
    image: mongo:7.0
    container_name: mongo-test-rs
    command: mongod --replSet testrs --port 27018 --dbpath /data/db
    ports:
      - "27018:27018"  # Different port from your main MongoDB
    environment:
      MONGO_INITDB_ROOT_USERNAME: testuser
      MONGO_INITDB_ROOT_PASSWORD: testpass
      MONGO_INITDB_DATABASE: introspection_test
    volumes:
      - mongo_test_data:/data/db
    networks:
      - test-network

  # Initialize the replica set
  mongo-test-setup:
    image: mongo:7.0
    depends_on:
      - mongo-test
    command: >
      bash -c "
        sleep 10 &&
        mongosh --host mongo-test:27018 --username testuser --password testpass --eval \"
          rs.initiate({
            _id: 'testrs',
            members: [{ _id: 0, host: 'mongo-test:27018' }]
          })
        \"
      "
    networks:
      - test-network

volumes:
  mongo_test_data:

networks:
  test-network:
    driver: bridge
